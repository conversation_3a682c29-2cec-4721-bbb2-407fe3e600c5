package com.sgmw.common

import android.annotation.SuppressLint
import android.content.Context
import androidx.multidex.MultiDexApplication

/**
 * @author: 董俊帅
 * @time: 2025/3/3
 * @desc: BaseApplication
 */
open class BaseApplication : MultiDexApplication() {

    companion object {
        // 全局Context
        @SuppressLint("StaticFieldLeak")
        lateinit var context: Context

        @SuppressLint("StaticFieldLeak")
        lateinit var application: BaseApplication
        const val TAG = "BaseApplication"

    }

    override fun attachBaseContext(base: Context) {
        super.attachBaseContext(base)
        // 使用Application Context而不是base Context，避免Context泄漏
        context = this
        application = this
    }


}